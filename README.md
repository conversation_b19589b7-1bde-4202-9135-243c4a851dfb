# 8进制编码辅助系统

<p align="center">
  <img src="app/favicon.ico" width="64" height="64" />
</p>

<p align="center">
  <strong>一个基于 Next.js 的复杂交互式网格系统</strong>
</p>

<p align="center">
  <img src="https://img.shields.io/badge/Next.js-14.2.23-black" />
  <img src="https://img.shields.io/badge/TypeScript-5-blue" />
  <img src="https://img.shields.io/badge/React-18-cyan" />
  <img src="https://img.shields.io/badge/Tailwind-3.4.1-teal" />
  <img src="https://img.shields.io/badge/Zustand-状态管理-purple" />
  <img src="https://img.shields.io/badge/Status-重构中-orange" />
</p>

---

## 📋 项目概述

8进制编码辅助系统是一个高度交互式的前端应用，提供33x33网格矩阵的可视化编辑和管理功能。系统支持多颜色分类、多层级管理、版本控制和数据持久化等复杂功能。

### 🎯 核心功能

- **大规模网格渲染**: 1089个可交互单元格的实时渲染
- **8色彩分类系统**: 红、青、黄、紫、橙、绿、蓝、粉色独立管理
- **4层级架构**: Level 1-4 的层次化数据组织
- **版本控制系统**: 多版本保存、切换和管理
- **数据持久化**: LocalStorage 自动保存和恢复
- **高级交互**: 悬停信息、单元格选择、批量操作

### 🏗️ 技术架构

```
8进制编码辅助系统
├── 33x33 网格系统 (1089个单元格)
├── 8种颜色分类 × 4个层级 = 32个数据子系统
├── 版本控制 & 数据持久化
└── 实时交互 & 性能优化
```

## 🚨 当前状态：重构进行中

### 重构进展

- **✅ R0阶段**: 紧急修复与性能优化 (100%完成)
  - 97%+性能提升，16个重复函数删除，15个颜色级别修复
- **✅ R1.1阶段**: Grid组件系统 (100%完成)
  - 305行专业组件代码，完整网格功能模块化
- **✅ R1.2阶段**: 控制面板组件系统 (95%完成)
  - 957行组件代码，8种颜色面板统一架构
- **🚀 R1.2.3阶段**: 控制面板主文件集成 (进行中)
  - 预期减少1,000+行主文件代码
- **⏳ R1.3阶段**: UI组件库 (计划中)
- **⏳ R2阶段**: 状态管理现代化 (计划中)

### 架构现状

- **主文件**: `app/page.tsx` - 645行 (从7400+行减少)
- **组件系统**: 13个专业组件，1,262行高质量组件代码
- **状态管理**: 5个Zustand Store，共1,467行
- **钩子逻辑**: `usePageLogic.ts` - 276行

### 重构目标

- **✅ 组件化**: 拆分为 15+ 个可复用组件 (当前进度: 86.7%)
- **✅ 状态优化**: 使用 Zustand 替代 80+ 个useState (已完成)
- **✅ 性能提升**: React.memo + useMemo + useCallback (已实现)
- **✅ 类型安全**: 100% TypeScript 严格模式 (已实现)
- **⏳ 主文件精简**: 从645行减少到 <100行 (当前进度: 21.4%)

## 🚀 快速开始

### 环境要求

- Node.js 18+
- npm/yarn/pnpm/bun

### 安装与运行

```bash
# 克隆项目
git clone [repository-url]
cd project-1749543614822

# 安装依赖
npm install
# 或
bun install

# 启动开发服务器
npm run dev
# 或
bun dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

### 开发命令

```bash
# 开发
npm run dev

# 构建
npm run build

# 生产运行
npm run start

# 代码检查
npm run lint

# 代码格式化
npm run format
```

## 📁 项目结构

### 当前结构

```
project-1749543614822/
├── app/
│   ├── page.tsx           # 主应用 (645 行)
│   ├── layout.tsx         # 全局布局
│   ├── globals.css        # 全局样式
│   └── favicon.ico
├── components/
│   ├── Grid/              # 网格组件系统
│   │   ├── GridContainer.tsx
│   │   ├── GridCell.tsx
│   │   ├── GridOverlay.tsx
│   │   └── types.ts
│   └── ControlPanel/      # 控制面板组件系统
│       ├── ColorSystemPanel.tsx
│       ├── StylePanel.tsx         # R2样式面板
│       ├── BasicDataPanel.tsx     # R2基础数据面板
│       ├── CombinationBusinessPanel.tsx # R2组合业务面板
│       ├── VersionPanel.tsx
│       └── ...
├── stores/                # 状态管理
│   ├── colorStore.ts
│   ├── gridStore.ts
│   ├── interactionStore.ts
│   ├── uiStore.ts
│   └── versionStore.ts
├── hooks/
│   └── usePageLogic.ts    # 页面逻辑钩子
├── constants/             # 常量定义
│   ├── business/
│   ├── config/
│   ├── core/
│   └── ui/
├── utils/                 # 工具函数
│   ├── colorSystem.ts
│   ├── cellUtils.ts
│   └── styleUtils.ts
├── types/                 # 类型定义
│   ├── color.ts
│   ├── grid.ts
│   └── version.ts
└── docs/                  # 项目文档
    ├── project-analysis.md
    ├── coding-log.md
    └── task-planning.md
```

## 📈 重构进度

### ✅ 已完成

- [x] 项目深度架构分析
- [x] 性能优化 (97%+函数调用减少)
- [x] 状态管理现代化 (Zustand实现)
- [x] Grid组件系统
- [x] ControlPanel组件系统

### 🔄 进行中

- [ ] 控制面板主文件集成
- [ ] 页面逻辑钩子完善
- [ ] 主文件简化

### 📅 计划中

- [ ] UI组件库开发
- [ ] 状态管理进一步优化
- [ ] 测试系统建立
- [ ] 文档完善

## 🎯 成功指标

### 代码质量

- **文件拆分**: 645行 → <100行主文件
- **组件化**: 13个组件 → 15+个组件
- **类型安全**: 100% TypeScript严格模式 ✅
- **代码规范**: 0 ESLint错误 ✅

### 性能指标

- **渲染性能**: 首屏加载 <1秒
- **交互响应**: 操作响应 <100ms
- **内存优化**: 内存使用减少 30%
- **包大小**: 构建产物减少 20%

### 开发体验

- **开发效率**: 新功能开发时间减少 50%
- **测试覆盖**: 代码覆盖率 >80%
- **文档完整**: 文档完整性 >90%
- **代码质量**: 重复率 <5%

## 🛠️ 技术栈

### 当前技术栈

- **框架**: Next.js 14.2.23 (App Router)
- **语言**: TypeScript 5
- **UI库**: React 18
- **样式**: Tailwind CSS 3.4.1
- **状态管理**: Zustand
- **工具**: clsx, tailwind-merge

### 计划引入

- **虚拟化**: react-window
- **测试**: Jest + Testing Library
- **状态工具**: Immer

## 📚 文档

- [项目架构分析](docs/project-analysis.md)
- [开发日志](docs/coding-log.md)
- [任务规划](docs/task-planning.md)

## 🤝 贡献指南

### 开发流程

1. **代码规范**: 遵循 ESLint 和 Prettier 配置
2. **类型安全**: 禁止使用 `any` 类型
3. **组件设计**: 遵循单一职责原则
4. **性能优化**: 使用 React.memo, useMemo, useCallback
5. **测试要求**: 新功能需要对应测试

### 代码审查清单

- [ ] TypeScript 严格模式通过
- [ ] ESLint 检查通过
- [ ] 组件函数 <100 行
- [ ] 文件长度 <500 行
- [ ] 有完整的类型定义
- [ ] 有对应的测试用例

## 🐛 问题报告

发现问题请通过以下方式报告：

1. 检查现有的 issues
2. 提供详细的重现步骤
3. 包含环境信息和错误日志
4. 如可能，提供修复建议

## 📄 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

---

## 🎉 全栈架构迁移完成！

### ✅ v2.0.0 - 全栈架构重大更新

项目已成功从纯前端架构迁移到完整的全栈架构！

#### 🏗️ 新增技术栈
- **后端**: Next.js API Routes + TypeScript
- **数据库**: Prisma ORM + SQLite(开发) + PostgreSQL(生产)
- **部署**: Vercel全栈部署
- **数据迁移**: LocalStorage → 数据库无缝迁移

#### 🚀 新增功能
- **API端点**: 完整的RESTful API
- **数据同步**: 实时LocalStorage + API双重存储
- **开发工具**: 内置API测试和调试面板 (Ctrl+Shift+D)
- **自动化**: 一键开发环境设置和部署

#### 📚 快速开始
```bash
# 一键设置开发环境
npm run dev:setup

# 启动开发服务器
npm run dev

# 打开开发工具面板
# 按 Ctrl+Shift+D
```

#### 🔗 重要链接
- **应用**: http://localhost:3000
- **API健康检查**: http://localhost:3000/api/health
- **数据库管理**: `npm run db:studio`
- **部署文档**: [docs/deployment.md](docs/deployment.md)

<p align="center">
  <strong>🎯 现在支持完整的全栈开发体验！</strong>
</p>
